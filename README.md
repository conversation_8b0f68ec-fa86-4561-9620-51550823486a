# 🐾 东方妖怪捉宠放置游戏

> 🎮 **基于 Cocos Creator 3.8.6 开发的东方妖怪题材挂机放置游戏**
> 🌸 **融合生产、战斗、宠物养成的深度挂机体验**
> 📱 **支持微信小程序 + 抖音小程序双平台**

## 🎯 游戏概述

### 🌟 **核心特色**
- **🏮 东方妖怪世界观**：古风仙侠背景，收集各种可爱妖怪
- **⚔️ 行动条战斗系统**：统一的时间管理机制，战斗与生产并重
- **🐾 智能捕捉系统**：预设捕捉策略，自动收集心仪妖怪
- **🏭 深度生产系统**：妖怪协助生产，打造专属妖怪农场
- **💤 真正的挂机体验**：离线收益，自动化管理

### 🎮 **核心玩法**
```
游戏玩法架构
├── 🗡️ 战斗系统 - 玩家+妖怪协同作战
├── 🏭 生产系统 - 采集、制作、炼化
├── 🐾 妖怪系统 - 捕捉、养成、进化
├── ⏱️ 行动条机制 - 统一的时间管理
└── 💤 挂机放置 - 离线自动收益
```

## 🛠️ 技术架构

### 📋 **开发环境**
- **游戏引擎**：Cocos Creator 3.8.6
- **开发语言**：TypeScript
- **目标平台**：微信小程序、抖音小程序
- **服务端**：Node.js + Express
- **数据库**：MongoDB

### 🏗️ **项目结构**
```
COCOS_IdleGame/
├── assets/           # 游戏资源
├── scripts/          # 游戏脚本
├── rules/           # 开发规范文档
├── docs/            # 项目文档
└── server/          # 服务端代码
```

## 🎨 游戏系统设计

### 🐾 **妖怪系统**
- **捕捉机制**：战斗胜利后的捕捉环节
- **养成系统**：等级、属性、技能树、进化
- **功能定位**：战斗伙伴 + 生产助手 + 收集要素
- **预设系统**：智能捕捉配置，自动化收集

### ⚔️ **战斗系统**
- **协同战斗**：玩家角色 + 妖怪伙伴
- **行动条机制**：统一的时间管理和行动排序
- **策略深度**：技能搭配、妖怪组合、时机把握

### 🏭 **生产系统**
- **资源采集**：采药、挖矿、捕鱼等
- **物品制作**：炼制丹药、制作装备
- **妖怪协助**：指派妖怪参与生产，提高效率

## 📚 文档导航

- [📋 开发规范](./rules/README.md) - 完整的开发规范和代码标准
- [🎮 游戏设计文档](./docs/game-design.md) - 详细的游戏系统设计
- [🔧 技术文档](./docs/technical.md) - 技术架构和实现细节
- [🚀 部署指南](./docs/deployment.md) - 项目部署和发布流程

## 🚀 快速开始

### 📦 **环境准备**
```bash
# 安装 Cocos Creator 3.8.6
# 克隆项目
git clone [项目地址]
cd COCOS_IdleGame

# 安装依赖
npm install
```

### 🎮 **运行游戏**
```bash
# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 发布到微信小程序
npm run build:wechat
```

## 🎯 开发进度

- [x] 🏗️ 项目架构搭建
- [x] ⚔️ 基础战斗系统
- [ ] 🐾 妖怪捕捉系统
- [ ] 🏭 生产系统
- [ ] ⏱️ 行动条机制
- [ ] 💤 挂机系统
- [ ] 📱 小程序适配

## 👥 开发团队

- **策划设计**：张睿哲
- **程序开发**：[开发者姓名]
- **美术设计**：[美术师姓名]

## 📄 许可证

本项目采用 [MIT License](./LICENSE) 许可证。

---

*🌸 在东方妖怪的世界里，与可爱的妖怪们一起冒险吧！*