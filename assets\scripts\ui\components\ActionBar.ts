/**
 * 行动条UI组件
 * 用于显示角色行动进度，支持动画效果和自定义样式
 */

import { _decorator, Component, Node, ProgressBar, Label, tween, Vec3, Color } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 行动条配置接口
 */
export interface IActionBarConfig {
    /** 行动条总时长（秒） */
    duration: number;
    
    /** 行动条颜色 */
    color?: Color;
    
    /** 背景颜色 */
    backgroundColor?: Color;
    
    /** 是否显示文本 */
    showText?: boolean;
    
    /** 文本格式 */
    textFormat?: string; // 例如: "{current}s / {total}s" 或 "{percent}%"
    
    /** 是否启用动画 */
    enableAnimation?: boolean;
    
    /** 动画类型 */
    animationType?: 'smooth' | 'pulse' | 'glow';
}

/**
 * 行动条状态枚举
 */
export enum ActionBarState {
    Idle = 'idle',
    Running = 'running',
    Paused = 'paused',
    Completed = 'completed'
}

@ccclass('ActionBar')
export class ActionBar extends Component {
    
    @property({ type: ProgressBar, tooltip: '进度条组件' })
    public progressBar: ProgressBar | null = null;
    
    @property({ type: Label, tooltip: '进度文本标签' })
    public progressLabel: Label | null = null;
    
    @property({ type: Node, tooltip: '动画容器节点' })
    public animationContainer: Node | null = null;
    
    @property({ tooltip: '默认持续时间（秒）' })
    public defaultDuration: number = 5.0;
    
    @property({ tooltip: '是否自动开始' })
    public autoStart: boolean = false;
    
    @property({ tooltip: '是否循环播放' })
    public loop: boolean = false;

    @property({ tooltip: '更新频率（FPS）', min: 10, max: 120 })
    public updateFPS: number = 60;

    @property({ tooltip: '启用平滑插值' })
    public enableSmoothing: boolean = true;

    @property({ tooltip: '平滑系数（0-1）', min: 0.1, max: 1.0 })
    public smoothingFactor: number = 0.8;
    
    // 私有属性
    private _config: IActionBarConfig = {
        duration: 5.0,
        showText: true,
        textFormat: "{current}s / {total}s",
        enableAnimation: true,
        animationType: 'smooth'
    };

    // 平滑插值相关
    private _displayProgress: number = 0; // 显示的进度（用于平滑插值）
    private _targetProgress: number = 0;  // 目标进度
    
    private _state: ActionBarState = ActionBarState.Idle;
    private _currentTime: number = 0;
    private _totalTime: number = 0;
    private _isRunning: boolean = false;
    private _isPaused: boolean = false;
    
    // 回调函数
    private _onProgressCallback: ((progress: number) => void) | null = null;
    private _onCompleteCallback: (() => void) | null = null;
    private _onStartCallback: (() => void) | null = null;
    private _onPauseCallback: (() => void) | null = null;
    private _onResumeCallback: (() => void) | null = null;

    protected onLoad(): void {
        console.log('🎯 ActionBar: 行动条组件加载');
        this.initializeComponents();
        this.setupDefaultConfig();
    }

    protected start(): void {
        if (this.autoStart) {
            this.startProgress();
        }
    }

    protected onDestroy(): void {
        this.stopProgress();
        this.clearCallbacks();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找进度条组件
        if (!this.progressBar) {
            this.progressBar = this.node.getComponent(ProgressBar);
            if (!this.progressBar) {
                this.progressBar = this.node.getComponentInChildren(ProgressBar);
            }
        }
        
        // 自动查找标签组件
        if (!this.progressLabel) {
            this.progressLabel = this.node.getComponent(Label);
            if (!this.progressLabel) {
                this.progressLabel = this.node.getComponentInChildren(Label);
            }
        }
        
        // 设置动画容器
        if (!this.animationContainer) {
            this.animationContainer = this.node;
        }
        
        console.log('🎯 ActionBar: 组件初始化完成', {
            progressBar: !!this.progressBar,
            progressLabel: !!this.progressLabel,
            animationContainer: !!this.animationContainer
        });
    }

    /**
     * 设置默认配置
     */
    private setupDefaultConfig(): void {
        this._config.duration = this.defaultDuration;
        this._totalTime = this.defaultDuration;
        
        // 初始化进度条
        if (this.progressBar) {
            this.progressBar.progress = 0;
        }
        
        // 初始化文本
        this.updateProgressText();
    }

    /**
     * 配置行动条
     */
    public configure(config: Partial<IActionBarConfig>): void {
        this._config = { ...this._config, ...config };
        this._totalTime = this._config.duration;
        
        // 应用颜色配置
        if (this.progressBar && config.color) {
            // 这里可以设置进度条颜色，具体实现取决于ProgressBar的设置
        }
        
        console.log('🎯 ActionBar: 配置更新', this._config);
    }

    /**
     * 开始进度
     */
    public startProgress(): void {
        if (this._isRunning && !this._isPaused) {
            console.warn('🎯 ActionBar: 进度已在运行中');
            return;
        }

        console.log('🎯 ActionBar: 开始进度');

        this._state = ActionBarState.Running;
        this._isRunning = true;
        this._isPaused = false;

        if (this._currentTime === 0) {
            this._currentTime = 0;
        }

        // 触发开始回调
        if (this._onStartCallback) {
            this._onStartCallback();
        }

        // 开始动画
        if (this._config.enableAnimation) {
            this.startAnimation();
        }

        // 开始计时 - 使用可配置的更新频率
        const updateInterval = 1 / this.updateFPS;
        this.schedule(this.updateProgress, updateInterval);
    }

    /**
     * 暂停进度
     */
    public pauseProgress(): void {
        if (!this._isRunning || this._isPaused) {
            return;
        }
        
        console.log('🎯 ActionBar: 暂停进度');
        
        this._state = ActionBarState.Paused;
        this._isPaused = true;
        
        this.unschedule(this.updateProgress);
        
        // 触发暂停回调
        if (this._onPauseCallback) {
            this._onPauseCallback();
        }
    }

    /**
     * 恢复进度
     */
    public resumeProgress(): void {
        if (!this._isRunning || !this._isPaused) {
            return;
        }

        console.log('🎯 ActionBar: 恢复进度');

        this._state = ActionBarState.Running;
        this._isPaused = false;

        const updateInterval = 1 / this.updateFPS;
        this.schedule(this.updateProgress, updateInterval);

        // 触发恢复回调
        if (this._onResumeCallback) {
            this._onResumeCallback();
        }
    }

    /**
     * 停止进度
     */
    public stopProgress(): void {
        console.log('🎯 ActionBar: 停止进度');
        
        this._state = ActionBarState.Idle;
        this._isRunning = false;
        this._isPaused = false;
        this._currentTime = 0;
        
        this.unschedule(this.updateProgress);
        this.stopAnimation();
        
        // 重置进度条
        if (this.progressBar) {
            this.progressBar.progress = 0;
        }
        
        this.updateProgressText();
    }

    /**
     * 重置进度
     */
    public resetProgress(): void {
        this.stopProgress();
        this._currentTime = 0;
        this._displayProgress = 0;
        this._targetProgress = 0;

        if (this.progressBar) {
            this.progressBar.progress = 0;
        }

        this.updateProgressText();
    }

    /**
     * 更新进度
     */
    private updateProgress(): void {
        if (!this._isRunning || this._isPaused) {
            return;
        }

        // 使用可配置的时间步长
        const deltaTime = 1 / this.updateFPS;
        this._currentTime += deltaTime;
        this._targetProgress = Math.min(this._currentTime / this._totalTime, 1.0);

        // 平滑插值处理
        if (this.enableSmoothing) {
            // 使用线性插值让进度条更平滑
            const lerpSpeed = this.smoothingFactor;
            this._displayProgress += (this._targetProgress - this._displayProgress) * lerpSpeed;

            // 当接近目标值时直接设置，避免无限接近
            if (Math.abs(this._targetProgress - this._displayProgress) < 0.001) {
                this._displayProgress = this._targetProgress;
            }
        } else {
            this._displayProgress = this._targetProgress;
        }

        // 更新进度条
        if (this.progressBar) {
            this.progressBar.progress = this._displayProgress;
        }

        // 更新文本（使用实际进度而不是显示进度）
        this.updateProgressText();

        // 触发进度回调（使用实际进度）
        if (this._onProgressCallback) {
            this._onProgressCallback(this._targetProgress);
        }

        // 检查是否完成（使用实际进度）
        if (this._targetProgress >= 1.0) {
            this.onProgressComplete();
        }
    }

    /**
     * 进度完成处理
     */
    private onProgressComplete(): void {
        console.log('🎯 ActionBar: 进度完成');
        
        this._state = ActionBarState.Completed;
        this.unschedule(this.updateProgress);
        
        // 触发完成回调
        if (this._onCompleteCallback) {
            this._onCompleteCallback();
        }
        
        // 处理循环
        if (this.loop) {
            this.scheduleOnce(() => {
                this.resetProgress();
                this.startProgress();
            }, 0.5);
        } else {
            this._isRunning = false;
        }
    }

    /**
     * 更新进度文本
     */
    private updateProgressText(): void {
        if (!this.progressLabel || !this._config.showText) {
            return;
        }
        
        const current = Math.floor(this._currentTime * 10) / 10;
        const total = this._totalTime;
        const percent = Math.floor((this._currentTime / this._totalTime) * 100);
        
        let text = this._config.textFormat || "{current}s / {total}s";
        text = text.replace('{current}', current.toFixed(1));
        text = text.replace('{total}', total.toFixed(1));
        text = text.replace('{percent}', percent.toString());
        
        this.progressLabel.string = text;
    }

    /**
     * 开始动画
     */
    private startAnimation(): void {
        if (!this._config.enableAnimation || !this.animationContainer) {
            return;
        }
        
        switch (this._config.animationType) {
            case 'pulse':
                this.startPulseAnimation();
                break;
            case 'glow':
                this.startGlowAnimation();
                break;
            case 'smooth':
            default:
                // 平滑动画已通过进度条本身实现
                break;
        }
    }

    /**
     * 脉冲动画
     */
    private startPulseAnimation(): void {
        if (!this.animationContainer) return;
        
        tween(this.animationContainer)
            .to(0.5, { scale: new Vec3(1.05, 1.05, 1) })
            .to(0.5, { scale: new Vec3(1, 1, 1) })
            .union()
            .repeatForever()
            .start();
    }

    /**
     * 发光动画
     */
    private startGlowAnimation(): void {
        // 这里可以实现发光效果，需要配合Shader或特殊材质
        console.log('🎯 ActionBar: 发光动画（需要Shader支持）');
    }

    /**
     * 停止动画
     */
    private stopAnimation(): void {
        if (this.animationContainer) {
            tween(this.animationContainer).stop();
            this.animationContainer.setScale(1, 1, 1);
        }
    }

    // ==================== 公共API ====================

    /**
     * 设置进度（0-1）
     */
    public setProgress(progress: number): void {
        progress = Math.max(0, Math.min(1, progress));
        this._currentTime = progress * this._totalTime;
        this._targetProgress = progress;

        // 如果不启用平滑插值，直接设置显示进度
        if (!this.enableSmoothing) {
            this._displayProgress = progress;
        }

        if (this.progressBar) {
            this.progressBar.progress = this.enableSmoothing ? this._displayProgress : progress;
        }

        this.updateProgressText();
    }

    /**
     * 获取当前进度（0-1）
     */
    public getProgress(): number {
        return this._currentTime / this._totalTime;
    }

    /**
     * 获取当前状态
     */
    public getState(): ActionBarState {
        return this._state;
    }

    /**
     * 是否正在运行
     */
    public isRunning(): boolean {
        return this._isRunning && !this._isPaused;
    }

    /**
     * 是否已暂停
     */
    public isPaused(): boolean {
        return this._isPaused;
    }

    // ==================== 回调设置 ====================

    public setOnProgressCallback(callback: (progress: number) => void): void {
        this._onProgressCallback = callback;
    }

    public setOnCompleteCallback(callback: () => void): void {
        this._onCompleteCallback = callback;
    }

    public setOnStartCallback(callback: () => void): void {
        this._onStartCallback = callback;
    }

    public setOnPauseCallback(callback: () => void): void {
        this._onPauseCallback = callback;
    }

    public setOnResumeCallback(callback: () => void): void {
        this._onResumeCallback = callback;
    }

    /**
     * 清除所有回调
     */
    private clearCallbacks(): void {
        this._onProgressCallback = null;
        this._onCompleteCallback = null;
        this._onStartCallback = null;
        this._onPauseCallback = null;
        this._onResumeCallback = null;
    }
}
